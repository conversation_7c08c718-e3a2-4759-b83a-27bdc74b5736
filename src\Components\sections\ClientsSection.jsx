import { useState, useEffect } from 'react';
import petrotalLogo from '../../assets/logos/petrotal_logo.png';
import ransaLogo from '../../assets/logos/ransa_logo.png';
import nexaLogo from '../../assets/logos/nexa_logo.png';
import alfaparfLogo from '../../assets/logos/alfaparf_logo.png';
import StarIcon from '../../assets/SVG/StarIcon';
import GearsIcon2 from '../../assets/SVG/GearsIcon2';
import PeopleIcon from '../../assets/SVG/PeopleIcon';
import CubeIcon from '../../assets/SVG/CubeIcon';
import LineCurve from '../../assets/SVG/LineCurve';
import './ClientsSection.css';

/**
 * Sección de clientes y enfoques
 * Muestra los logos de empresas que confían en Greta Labs
 * y los tres enfoques: Personas, Servicios y Productos
 */
const ClientsSection = () => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => setIsVisible(true), 300);
    return () => clearTimeout(timer);
  }, []);

  const companies = [
    { name: "PetroTal", logo: petrotalLogo },
    { name: "Ransa", logo: ransaLogo },
    { name: "Nexa", logo: nexaLogo },
    { name: "Alfaparf", logo: alfaparfLogo }
  ];

  const duplicatedCompanies = [...companies, ...companies];

  const approaches = [
    {
      icon: GearsIcon2,
      title: "Servicios",
      description: "Soluciones tecnológicas especializadas"
    },
    {
      icon: PeopleIcon,
      title: "Personas",
      description: "Equipos especializados y capacitados"
    },
    {
      icon: CubeIcon,
      title: "Productos",
      description: "Desarrollo de productos innovadores"
    }
  ];

  return (
    <section className={`clients-section ${isVisible ? 'visible' : ''}`}>
      <div className="clients-container">
        {/* Header */}
        <div className="clients-header">
          <h2 className="clients-title">Ellos Confían en Nosotros</h2>
          <div className="title-underline-clients"></div>
        </div>

        {/* Company Logos */}
        {/* <div className="companies-grid">
          {companies.map((company, index) => {
            return (
              <div
                key={company.name}
                className="company-logo"
                style={{ '--delay': `${index * 0.2}s` }}
              >
                <img
                  src={company.logo}
                  alt={`${company.name} logo`}
                  className="logo-image"
                />
              </div>
            );
          })}
        </div> */}

        <div 
          className="w-full mb-[60px]" 
          // style={{ background: '#000000' }}
        >
          {/* Company Logos Carousel - Centered Container */}
          <div className="companies-carousel-container-clients">
            <div className="companies-carousel-clients">
              <div className="carousel-track-clients">
                {duplicatedCompanies.map((company, index) => {
                  const LogoComponent = company.logo;
                  return (
                    <div
                      key={`${company.name}-${index}`}
                      // className="clc-clt"
                    >
                      <img
                        src={LogoComponent}
                        alt={`${company.name} logo`}
                        className="logo-image"
                      />
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        </div>

        {/* Description */}
        <div className='flex flex-col gap-4'>
          <div className="star-divider">
            <StarIcon width={40} height={40} />
          </div>
          <div className="clients-description">
            <p>
              En Greta Labs, tenemos tres enfoques: <span className="highlight">Personas, Servicios y Productos</span>. 
              Estos nos permiten desarrollar proyectos que realmente marcan la diferencia
            </p>
          </div>
        </div>

        {/* Approaches */}
        <div className="approaches-container">
          {/* Wave line background */}
          <div className="wave-line-container">
            <LineCurve />
          </div>

          {/* Icons positioned on the wave */}
          <div className="approaches-icons">
            <div
              key='Servicios'
              className={`approach-icon-wrapper approach-icon-1`}
              style={{ '--delay': `${1 * 0.3}s` }}
            >
              <div className="approach-icon">
                <GearsIcon2 width={80} height={80} />
              </div>
            </div>
            <div
              key='Personas'
              className={`approach-icon-wrapper approach-icon-2`}
              style={{ '--delay': `${2 * 0.3}s` }}
            >
              <div className="approach-icon">
                <PeopleIcon width={80} height={80} />
              </div>
            </div>
            <div
              key='Productos'
              className={`approach-icon-wrapper approach-icon-3`}
              style={{ '--delay': `${3 * 0.3}s` }}
            >
              <div className="approach-icon">
                <CubeIcon width={80} height={80} />
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ClientsSection;
