.macro-section-1 {
  width: 100%;
  min-height: 100vh;
  opacity: 0;
  transform: translateY(50px);
  transition: all 1s ease-out;
}

.macro-section-1.visible {
  opacity: 1;
  transform: translateY(0);
}

.macro-subsection {
  width: 100%;
  position: relative;
}

/* Asegurar que cada subsección tenga el espacio adecuado */
.macro-subsection:not(:last-child) {
  margin-bottom: 0;
}

/* Mantener la altura mínima de cada sección */
.macro-subsection > * {
  min-height: 100vh;
}

/* Responsive design */
@media (max-width: 768px) {
  .macro-section-1 {
    padding: 0;
  }
  
  .macro-subsection > * {
    min-height: 80vh;
  }
}
