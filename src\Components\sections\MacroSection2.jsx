import { useState, useEffect } from 'react';
import PrismaSection from './PrismaSection';
import CTASection from './CTASection';
import './MacroSection2.css';

/**
 * MacroSection2 - Combina PrismaSection y CTASection
 * Esta macrosección agrupa las secciones relacionadas con Prisma y el call-to-action final
 */
const MacroSection2 = () => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => setIsVisible(true), 300);
    return () => clearTimeout(timer);
  }, []);

  return (
    <div className={`macro-section-2 ${isVisible ? 'visible' : ''}`}>
      {/* Sección de Prisma */}
      <div className="macro-subsection">
        <PrismaSection />
      </div>

      {/* Sección CTA */}
      <div className="macro-subsection">
        <CTASection />
      </div>
    </div>
  );
};

export default MacroSection2;
