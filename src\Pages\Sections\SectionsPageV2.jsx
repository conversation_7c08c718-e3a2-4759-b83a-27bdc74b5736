import Header from '../../Components/layout/Header';
import LabSection from '../../Components/sections/LabSection';
import MacroSection1 from '../../Components/sections/MacroSection1';
import MacroSection2 from '../../Components/sections/MacroSection2';
import useMouseWheelScroll from '../../hooks/useMouseWheelScroll';
import './SectionsPageV2.css';

/**
 * Página de secciones V2 con macrosecciones
 * Incluye LabSection, MacroSection1 (Clients+Services+Methodology) y MacroSection2 (Prisma+CTA)
 * Mantiene el mismo comportamiento de scroll que la versión original
 */
const SectionsPageV2 = () => {
  // Array de componentes de secciones (ahora con macrosecciones)
  const sections = [
    LabSection,
    MacroSection1,
    MacroSection2
  ];

  // Hook para manejar el scroll tipo mouse wheel
  const {
    currentSection,
    isTransitioning,
    getSectionState,
    goToSection
  } = useMouseWheelScroll(sections.length);

  // Determinar si usar logo blanco basado en la sección actual
  // LabSection (índice 0) usa fondo oscuro, por lo que necesita logo blanco
  const useWhiteLogo = currentSection === 0;

  return (
    <div className={`sections-page-v2 ${isTransitioning ? 'transitioning' : ''}`}>
      {/* Header dinámico fijo */}
      <Header useWhiteLogo={useWhiteLogo} />

      {/* Contenedor de secciones con efecto mouse wheel */}
      <div className="sections-container-v2">
        {sections.map((SectionComponent, index) => (
          <div
            key={index}
            className={`section-wrapper-v2 ${getSectionState(index)}`}
            data-section={index}
          >
            <SectionComponent />
          </div>
        ))}
      </div>

      {/* Indicador de sección actual */}
      <div className="section-indicator-v2">
        {sections.map((_, index) => (
          <div
            key={index}
            className={`indicator-dot-v2 ${index === currentSection ? 'active' : ''}`}
            onClick={() => goToSection(index)}
            title={`Ir a sección ${index + 1}`}
          />
        ))}
      </div>
    </div>
  );
};

export default SectionsPageV2;
