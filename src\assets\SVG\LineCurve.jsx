/**
 * Componente SVG del ícono de personas
 * Usado en la sección de clientes para representar "Personas"
 */
const LineCurve = ({ width = 1035, height = 184, className = "" }) => {
  return (
    <svg width={width} height={height} viewBox="0 0 1035 184" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M0 65.5C0 67.7091 1.79086 69.5 4 69.5C6.20914 69.5 8 67.7091 8 65.5C8 63.2909 6.20914 61.5 4 61.5C1.79086 61.5 0 63.2909 0 65.5ZM1027 65.5C1027 67.7091 1028.79 69.5 1031 69.5C1033.21 69.5 1035 67.7091 1035 65.5C1035 63.2909 1033.21 61.5 1031 61.5C1028.79 61.5 1027 63.2909 1027 65.5ZM4 65.5L3.56755 66.1128L134.388 158.438L134.821 157.826L135.253 157.213L4.43245 64.8872L4 65.5ZM286.193 151.364L286.676 151.938L429.75 31.4542L429.267 30.8805L428.784 30.3069L285.71 150.79L286.193 151.364ZM587.061 29.2707L586.59 29.8541L742.394 155.716L742.866 155.133L743.337 154.549L587.532 28.6873L587.061 29.2707ZM890.604 161.151L891.026 161.771L1031.42 66.1198L1031 65.5L1030.58 64.8802L890.182 160.531L890.604 161.151ZM742.866 155.133L742.394 155.716C785.185 190.283 845.566 192.743 891.026 161.771L890.604 161.151L890.182 160.531C845.268 191.131 785.613 188.7 743.337 154.549L742.866 155.133ZM429.267 30.8805L429.75 31.4542C474.901 -6.56798 540.672 -7.23903 586.59 29.8541L587.061 29.2707L587.532 28.6873C541.056 -8.85731 474.485 -8.17809 428.784 30.3069L429.267 30.8805ZM134.821 157.826L134.388 158.438C180.727 191.141 243.293 188.47 286.676 151.938L286.193 151.364L285.71 150.79C242.849 186.884 181.034 189.523 135.253 157.213L134.821 157.826Z" fill="url(#paint0_linear_186_103)"/>
      <defs>
        <linearGradient id="paint0_linear_186_103" x1="4" y1="89" x2="1031" y2="89" gradientUnits="userSpaceOnUse">
          <stop stop-color="#B30FDC"/>
          <stop offset="1" stop-color="#156CFF"/>
        </linearGradient>
      </defs>
    </svg>
  );
};

export default LineCurve;
