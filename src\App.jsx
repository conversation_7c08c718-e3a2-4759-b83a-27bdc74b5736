import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import LandingPage from './Pages/Landing/LandingPage';
import SectionsPage from './Pages/Sections/SectionsPage';
import SectionsPageV2 from './Pages/Sections/SectionsPageV2';
import './styles/globals/reset.css';
import './styles/globals/variables.css';
import './App.css';

/**
 * Componente principal de la aplicación
 * Maneja las rutas entre la página principal y las secciones
 */
function App() {
  return (
    <Router>
      <div className="App">
        <Routes>
          {/* Página principal con HeroSection */}
          <Route path="/" element={<LandingPage />} />

          {/* Página de secciones apiladas */}
          <Route path="/empecemos" element={<SectionsPage />} />

          {/* Página de secciones v2 con macrosecciones */}
          <Route path="/empecemos/v2" element={<SectionsPageV2 />} />
        </Routes>
      </div>
    </Router>
  );
}

export default App;
