.clients-section {
  min-height: 100vh;
  background: #ffffff;
  padding: 80px 40px 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transform: translateY(50px);
  transition: all 1s ease-out;
}

.clients-section.visible {
  opacity: 1;
  transform: translateY(0);
}

.clients-container {
  max-width: 1200px;
  width: 100%;
  text-align: center;
}

/* Header */
.clients-header {
  margin-bottom: 20px;
}

.clients-title {
  font-size: 2.5rem;
  font-weight: 600;
  color: #272727;
  margin-bottom: 20px;
}

.title-underline-clients {
  width: 19rem;
  height: 4px;
  background: linear-gradient(90deg, #ADFF4D 0%, #17FFE4 100%);
  /* background: linear-gradient(90deg, #17FFE4 0%, #ADFF4D 100%); */
  margin: 0 auto;
  border-radius: 2px;
}

/* Company Logos */
.companies-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 40px;
  margin-bottom: 80px;
  align-items: center;
}

.company-logo {
  opacity: 0;
  transform: translateY(30px);
  animation: slideInUp 0.8s ease var(--delay) forwards;
  transition: transform 0.3s ease;
}

.company-logo:hover {
  transform: translateY(-5px);
}

/* Star Divider */
.star-divider {
  margin: 4rem 0 0 0;
  display: flex;
  justify-content: center;
  opacity: 0;
  animation: fadeIn 1s ease 0.5s forwards;
}

/* Description */
.clients-description {
  max-width: 60vw;
  margin: 0 auto 60px;
  opacity: 0;
  animation: slideInUp 0.8s ease 0.8s forwards;
}

.clients-description p {
  font-size: 1.8rem;
  font-weight: 500;
  line-height: 1.6;
  color: #272727;
}

.clients-description .highlight {
  background: linear-gradient(90deg, #C636FF 0%, #156CFF 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 600;
}

/* Approaches */
.approaches-container {
  position: relative;
  margin-top: 20px;
  width: 100%;
  max-width: 1035px;
  margin-left: auto;
  margin-right: auto;
  height: 200px;
}

.wave-line-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1;
  opacity: 0;
  animation: fadeIn 1s ease 1.2s forwards;
}

.approaches-icons {
  position: relative;
  z-index: 2;
  width: 100%;
  height: 100%;
}

.approach-icon-wrapper {
  position: absolute;
  opacity: 0;
  transform: translateY(50px);
  animation: slideInUp 0.8s ease var(--delay) forwards;
  transition: all 0.3s ease;
}

.approach-icon-wrapper:hover {
  transform: scale(1.1) translateY(-5px);
}

.approach-icon {
  display: flex;
  justify-content: center;
  align-items: center;
}

/* Position icons on the wave curve */
.approach-icon-1 {
  left: 8rem;
  top: 0.1rem;
}

.approach-icon-2 {
  left: 27rem;
  transform: translateX(-50%);
  top: .9rem;
}

.approach-icon-3 {
  right: 9rem;
  top: 0.1rem;
}

.approach-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 15px;
}

.approach-description {
  font-size: 1rem;
  color: #666;
  line-height: 1.5;
}

/* Company Logos Carousel - Centered Container */
.companies-carousel-container-clients {
  width: 100%;
  /* max-width: 850px; */
  margin: 0 auto 0px auto;
  /* background: #000000; */
  padding: 30px 0;
  overflow: hidden;
  border-radius: 5px;
  z-index: 10;
}

.companies-carousel-clients {
  width: 100%;
  overflow: hidden;
  position: relative;
}

.carousel-track-clients {
  display: flex;
  align-items: center;
  gap: 80px;
  animation: scrollLeft 16s linear infinite;
  width: calc(200% + 160px); /* Ancho para 8 logos + gaps */
}

.clc-clt {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 120px;
}

.clc-clt .logo-image {
  filter: brightness(0) invert(0);
  /* opacity: 0.8; */
  transition: opacity 0.3s ease;
  max-height: 45px;
  width: auto;
}

.clc-clt:hover .logo-image {
  opacity: 1;
}

/* Animations */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Responsive */
@media (max-width: 768px) {
  .clients-section {
    padding: 60px 20px;
  }

  .clients-title {
    font-size: 2.5rem;
  }

  .companies-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
  }

  .clients-description p {
    font-size: 1.1rem;
  }

  .approaches-container {
    height: 150px;
    margin-top: 20px;
  }

  .approach-icon-wrapper {
    transform: scale(0.8);
  }

  .approach-icon-wrapper:hover {
    transform: scale(0.9) translateY(-5px);
  }

  .approach-icon-1 {
    left: 8rem;
  }

  .approach-icon-2 {
    left: 27rem;
  }

  .approach-icon-3 {
    right: 9rem;
  }
}
