import { useState, useEffect } from 'react';
import ClientsSection from './ClientsSection';
import ServicesSection from './ServicesSection';
import MethodologySection from './MethodologySection';
import './MacroSection1.css';

/**
 * MacroSection1 - Combina ClientsSection, ServicesSection y MethodologySection
 * Esta macrosección agrupa las secciones relacionadas con clientes, servicios y metodología
 */
const MacroSection1 = () => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => setIsVisible(true), 300);
    return () => clearTimeout(timer);
  }, []);

  return (
    <div className={`macro-section-1 ${isVisible ? 'visible' : ''}`}>
      {/* Sección de Clientes */}
      <div className="macro-subsection">
        <ClientsSection />
      </div>

      {/* Sección de Servicios */}
      <div className="macro-subsection">
        <ServicesSection />
      </div>

      {/* Sección de Metodología */}
      <div className="macro-subsection">
        <MethodologySection />
      </div>
    </div>
  );
};

export default MacroSection1;
